package xtrabackup

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"mdc-agent/library/logger"
)

// XtraBackupManager XtraBackup 管理器实现
type XtraBackupManager struct {
	config         *Config
	limiter        *RateLimiter
	monitor        *ProgressMonitor
	processManager *ProcessManager
	replicationMgr *ReplicationManager
	status         BackupStatus
	mutex          sync.RWMutex
	pauseFile      string
	stopChan       chan struct{}
}

// NewManager 创建新的 XtraBackup 管理器
func NewManager(config *Config) *XtraBackupManager {
	return &XtraBackupManager{
		config:         config,
		limiter:        NewRateLimiter(config.ThrottleRate),
		monitor:        NewProgressMonitor(),
		processManager: NewProcessManager(),
		replicationMgr: NewReplicationManager(config),
		status:         StatusIdle,
		pauseFile:      "/tmp/xtrabackup_pause",
		stopChan:       make(chan struct{}),
	}
}

// FullBackup 执行全量备份
func (m *XtraBackupManager) FullBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.status == StatusRunning {
		return nil, fmt.Errorf("backup is already running")
	}

	// 设置备份类型为全量
	opts.Type = BackupTypeFull
	opts.LSNPosition = "0"

	return m.executeBackup(ctx, opts)
}

// IncrementalBackup 执行增量备份
func (m *XtraBackupManager) IncrementalBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error) {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	if m.status == StatusRunning {
		return nil, fmt.Errorf("backup is already running")
	}

	if opts.LSNPosition == "" || opts.LSNPosition == "0" {
		return nil, fmt.Errorf("incremental backup requires valid LSN position")
	}

	// 设置备份类型为增量
	opts.Type = BackupTypeIncremental

	return m.executeBackup(ctx, opts)
}

// executeBackup 执行备份的核心逻辑
func (m *XtraBackupManager) executeBackup(ctx context.Context, opts *BackupOptions) (*BackupResult, error) {
	result := &BackupResult{
		StartTime: time.Now(),
	}

	// 1. 检查是否有正在运行的备份进程
	if running, err := m.CheckProcess(); err != nil {
		return nil, fmt.Errorf("failed to check backup process: %w", err)
	} else if running {
		return nil, fmt.Errorf("backup process is already running")
	}

	// 2. 创建必要的目录
	if err := m.createDirectories(opts); err != nil {
		return nil, fmt.Errorf("failed to create directories: %w", err)
	}

	// 3. 检查并管理并行复制
	if err := m.replicationMgr.ManageParallelReplication(true); err != nil {
		logger.Warn("Failed to manage parallel replication: %v", err)
	}

	// 4. 构建并执行备份命令
	m.status = StatusRunning
	m.monitor.SetStatus(StatusRunning)

	cmd, err := m.buildBackupCommand(opts)
	if err != nil {
		m.status = StatusFailed
		return nil, fmt.Errorf("failed to build backup command: %w", err)
	}

	// 5. 执行备份
	_, err = m.executeCommand(ctx, cmd)
	if err != nil {
		m.status = StatusFailed
		result.Success = false
		result.ErrorMsg = err.Error()

		// 恢复并行复制设置
		m.replicationMgr.ManageParallelReplication(false)
		return result, err
	}

	// 6. 解析备份结果
	if err := m.parseBackupResult(opts, result); err != nil {
		logger.Warn("Failed to parse backup result: %v", err)
	}

	// 7. 恢复并行复制设置
	if err := m.replicationMgr.ManageParallelReplication(false); err != nil {
		logger.Warn("Failed to restore parallel replication: %v", err)
	}

	m.status = StatusCompleted
	result.Success = true
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)

	logger.Info("Backup completed successfully: %+v", result)
	return result, nil
}

// buildBackupCommand 构建备份命令
func (m *XtraBackupManager) buildBackupCommand(opts *BackupOptions) (string, error) {
	var cmdParts []string

	// 	./xtrabackup
	// 	--defaults-file=/path/to/my.cnf
	// 	--socket=/path/to/mysql.sock
	// 	-uroot -p'your_password'
	// 	--compress
	// 	--compress-threads=4
	// 	--parallel=16
	// 	--backup
	// 	--slave-info
	// 	--lock-ddl-per-table
	// 	--stream=xbstream

	// 	|
	// 	ssh user@remote_host "cat > /path/to/backup.xbstream" > xtrabackup_output.log 2> xtrabackup_error.log

	// --incremental --incremental-basedir=/path/to/full_backup

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s/etc/my.cnf", m.config.MySQLBaseDir))
	cmdParts = append(cmdParts, fmt.Sprintf("--user=%s", m.config.MySQLUser))
	cmdParts = append(cmdParts, fmt.Sprintf("--password=\"%s\"", m.config.MySQLPassword))
	cmdParts = append(cmdParts, fmt.Sprintf("--socket=%s", m.config.MySQLSocket))
	cmdParts = append(cmdParts, "--backup")

	// 性能和安全选项
	if m.config.KillLongQueryType != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--kill-long-query-type=%s", m.config.KillLongQueryType))
	}
	if m.config.KillLongQueryTimeout != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--kill-long-queries-timeout=\"%s\"", m.config.KillLongQueryTimeout))
	}

	// 备份选项
	if m.config.NoTimestamp {
		cmdParts = append(cmdParts, "--no-timestamp")
	}
	if m.config.SlaveInfo {
		cmdParts = append(cmdParts, "--slave-info")
	}

	// LSN 目录
	if opts.ExtraLSNDir != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--extra-lsndir=%s", opts.ExtraLSNDir))
	}

	// 增量备份选项
	if opts.Type == BackupTypeIncremental {
		cmdParts = append(cmdParts, "--incremental")
		cmdParts = append(cmdParts, fmt.Sprintf("--incremental-lsn=%s", opts.LSNPosition))
	}

	// 流和压缩选项
	if m.config.StreamFormat != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--stream=%s", m.config.StreamFormat))
	}
	if m.config.CompressLevel > 0 {
		cmdParts = append(cmdParts, "--compress")
		cmdParts = append(cmdParts, fmt.Sprintf("--compress-threads=%d", m.config.CompressThreads))
	}

	// 限速选项
	if m.config.ThrottleRate > 0 {
		cmdParts = append(cmdParts, fmt.Sprintf("--throttle=%d", m.config.ThrottleRate))
	}

	// 额外选项
	if opts.BackupOptions != "" {
		cmdParts = append(cmdParts, opts.BackupOptions)
	}
	if m.config.ExtraOptions != "" {
		cmdParts = append(cmdParts, m.config.ExtraOptions)
	}

	// 备份动作和目标

	cmdParts = append(cmdParts, opts.TargetDir)

	// 如果有远程传输，添加 SSH 管道
	cmd := strings.Join(cmdParts, " ")
	if opts.RemoteHost != "" && opts.RemotePath != "" {
		remoteFile := fmt.Sprintf("%s.xbstream", opts.RemotePath)
		sshCmd := fmt.Sprintf("ssh -i %s -o StrictHostKeyChecking=no mysql@%s \"cat - > %s\"",
			opts.SSHKeyPath, opts.RemoteHost, remoteFile)
		cmd = fmt.Sprintf("%s | %s", cmd, sshCmd)
	}

	return cmd, nil
}

// createDirectories 创建必要的目录
func (m *XtraBackupManager) createDirectories(opts *BackupOptions) error {
	if opts.ExtraLSNDir != "" {
		if err := os.MkdirAll(opts.ExtraLSNDir, 0755); err != nil {
			return fmt.Errorf("failed to create extra LSN directory: %w", err)
		}
	}

	if opts.TargetDir != "" {
		if err := os.MkdirAll(opts.TargetDir, 0755); err != nil {
			return fmt.Errorf("failed to create target directory: %w", err)
		}
	}

	return nil
}

// executeCommand 执行命令
func (m *XtraBackupManager) executeCommand(ctx context.Context, cmdStr string) (string, error) {
	cmd := exec.CommandContext(ctx, "bash", "-c", cmdStr)

	// 记录进程信息
	m.processManager.SetCommand(cmdStr)

	output, err := cmd.CombinedOutput()
	if err != nil {
		return string(output), fmt.Errorf("command execution failed: %w, output: %s", err, string(output))
	}

	return string(output), nil
}

// parseBackupResult 解析备份结果
func (m *XtraBackupManager) parseBackupResult(opts *BackupOptions, result *BackupResult) error {
	if opts.ExtraLSNDir == "" {
		return nil
	}

	infoFile := filepath.Join(opts.ExtraLSNDir, "xtrabackup_info")
	xtraInfo, err := ParseXtraInfo(infoFile)
	if err != nil {
		return fmt.Errorf("failed to parse xtrabackup_info: %w", err)
	}

	result.DataSize = xtraInfo.DataSize
	result.LSNFrom = xtraInfo.LSNFrom
	result.LSNTo = xtraInfo.LSNTo

	if opts.RemoteHost != "" && opts.RemotePath != "" {
		result.BackupPath = fmt.Sprintf("%s.xbstream", opts.RemotePath)
	} else {
		result.BackupPath = opts.TargetDir
	}

	return nil
}
