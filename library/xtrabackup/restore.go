package xtrabackup

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

// PrepareBackup 准备备份（apply-log）
func (m *XtraBackupManager) PrepareBackup(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error) {
	result := &RestoreResult{
		StartTime: time.Now(),
	}

	// 1. 准备全量备份
	if err := m.applyLogToBackup(opts.BackupPath, true, len(opts.IncrementalDirs) == 0); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 2. 依次应用增量备份
	for i, incDir := range opts.IncrementalDirs {
		isLast := (i == len(opts.IncrementalDirs)-1)
		if err := m.applyIncrementalBackup(opts.BackupPath, incDir, isLast); err != nil {
			result.Success = false
			result.ErrorMsg = err.Error()
			return result, err
		}
	}

	result.Success = true
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.DataPath = opts.BackupPath

	return result, nil
}

// RestoreBackup 恢复备份（move-back）
func (m *XtraBackupManager) RestoreBackup(ctx context.Context, opts *RestoreOptions) (*RestoreResult, error) {
	result := &RestoreResult{
		StartTime: time.Now(),
	}

	// 1. 修改配置文件
	if err := m.modifyBackupConfig(opts.BackupPath, opts.TargetDir); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	// 2. 执行 move-back
	if err := m.moveBackData(opts.BackupPath, opts.TargetDir); err != nil {
		result.Success = false
		result.ErrorMsg = err.Error()
		return result, err
	}

	result.Success = true
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	result.DataPath = opts.TargetDir

	return result, nil
}

// applyLogToBackup 对备份应用日志
func (m *XtraBackupManager) applyLogToBackup(backupPath string, _, isLast bool) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)

	// 配置文件
	configFile := filepath.Join(backupPath, "backup-my.cnf")
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))

	// apply-log 操作
	cmdParts = append(cmdParts, "--apply-log")

	// 如果不是最后一个备份，使用 --redo-only
	if !isLast {
		cmdParts = append(cmdParts, "--redo-only")
	}

	// 内存使用
	if m.config.UseMemory != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--use-memory=%s", m.config.UseMemory))
	}

	// 目标目录
	cmdParts = append(cmdParts, backupPath)

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Applying log to backup: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	})
	if err != nil {
		return fmt.Errorf("apply-log failed: %w, output: %s", err, output)
	}

	logger.Info("Apply-log completed successfully")
	return nil
}

// applyIncrementalBackup 应用增量备份
func (m *XtraBackupManager) applyIncrementalBackup(basePath, incrementalPath string, isLast bool) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)

	// 配置文件
	configFile := filepath.Join(incrementalPath, "backup-my.cnf")
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))

	// apply-log 操作
	cmdParts = append(cmdParts, "--apply-log")

	// 如果不是最后一个增量备份，使用 --redo-only
	if !isLast {
		cmdParts = append(cmdParts, "--redo-only")
	}

	// 内存使用
	if m.config.UseMemory != "" {
		cmdParts = append(cmdParts, fmt.Sprintf("--use-memory=%s", m.config.UseMemory))
	}

	// 增量目录
	cmdParts = append(cmdParts, fmt.Sprintf("--incremental-dir=%s", incrementalPath))

	// 基础目录
	cmdParts = append(cmdParts, basePath)

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Applying incremental backup: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	})
	if err != nil {
		return fmt.Errorf("incremental apply-log failed: %w, output: %s", err, output)
	}

	logger.Info("Incremental apply-log completed successfully")
	return nil
}

// modifyBackupConfig 修改备份配置文件
func (m *XtraBackupManager) modifyBackupConfig(backupPath, targetDir string) error {
	configFile := filepath.Join(backupPath, "backup-my.cnf")

	// 删除 innodb_undo_directory 配置
	removeUndoCmd := fmt.Sprintf("sed -i '/innodb_undo_directory=/d' %s", configFile)
	if _, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", removeUndoCmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	}); err != nil {
		return fmt.Errorf("failed to remove innodb_undo_directory: %w", err)
	}

	// 添加 datadir 配置
	addDatadirCmd := fmt.Sprintf("sed -i '$adatadir=%s/var' %s", targetDir, configFile)
	if _, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", addDatadirCmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	}); err != nil {
		return fmt.Errorf("failed to add datadir: %w", err)
	}

	logger.Info("Backup configuration modified successfully")
	return nil
}

// moveBackData 执行 move-back 操作
func (m *XtraBackupManager) moveBackData(backupPath, _ string) error {
	var cmdParts []string

	// 基础命令
	cmdParts = append(cmdParts, m.config.BinPath)

	// 配置文件
	configFile := filepath.Join(backupPath, "backup-my.cnf")
	cmdParts = append(cmdParts, fmt.Sprintf("--defaults-file=%s", configFile))

	// move-back 操作
	cmdParts = append(cmdParts, "--move-back")

	// 源目录
	cmdParts = append(cmdParts, backupPath)

	cmd := strings.Join(cmdParts, " ")
	logger.Info("Executing move-back: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	})
	if err != nil {
		return fmt.Errorf("move-back failed: %w, output: %s", err, output)
	}

	// move-back 完成后删除源目录
	if err := os.RemoveAll(backupPath); err != nil {
		logger.Warn("Failed to remove backup directory after move-back: %v", err)
	}

	logger.Info("Move-back completed successfully")
	return nil
}

// DecompressBackup 解压备份文件
func (m *XtraBackupManager) DecompressBackup(backupPath string, parallelThreads int) error {
	// 检查是否为 xbstream 格式
	if strings.Contains(backupPath, "xbstream") {
		if err := m.extractXbstream(backupPath); err != nil {
			return fmt.Errorf("failed to extract xbstream: %w", err)
		}
	}

	// 解压缩文件
	if err := m.decompressFiles(backupPath, parallelThreads); err != nil {
		return fmt.Errorf("failed to decompress files: %w", err)
	}

	return nil
}

// extractXbstream 解压 xbstream 文件
func (m *XtraBackupManager) extractXbstream(xbstreamFile string) error {
	// 获取目标目录
	targetDir := strings.TrimSuffix(xbstreamFile, ".xbstream")

	// 创建目标目录
	if err := os.MkdirAll(targetDir, 0755); err != nil {
		return fmt.Errorf("failed to create target directory: %w", err)
	}

	// 解压 xbstream
	cmd := fmt.Sprintf("%s -x < %s", m.config.XbstreamPath, xbstreamFile)

	// 切换到目标目录执行
	oldDir, err := os.Getwd()
	if err != nil {
		return fmt.Errorf("failed to get current directory: %w", err)
	}

	if err := os.Chdir(targetDir); err != nil {
		return fmt.Errorf("failed to change directory: %w", err)
	}
	defer os.Chdir(oldDir)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	})
	if err != nil {
		return fmt.Errorf("xbstream extraction failed: %w, output: %s", err, output)
	}

	logger.Info("Xbstream extraction completed successfully")
	return nil
}

// decompressFiles 解压缩文件
func (m *XtraBackupManager) decompressFiles(backupPath string, parallelThreads int) error {
	// 构建解压命令
	cmd := fmt.Sprintf("export PATH=$PATH:%s && %s --decompress --remove-original --parallel=%d %s/",
		filepath.Dir(m.config.BinPath), m.config.BinPath, parallelThreads, backupPath)

	logger.Info("Decompressing files: %s", cmd)

	output, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", cmd},
		Timeout: 5 * time.Hour,
		Retry:   3,
	})
	if err != nil {
		return fmt.Errorf("decompression failed: %w, output: %s", err, output)
	}

	logger.Info("File decompression completed successfully")
	return nil
}

// ValidateBackup 验证备份完整性
func (m *XtraBackupManager) ValidateBackup(backupPath string) error {
	// 检查必要的文件是否存在
	requiredFiles := []string{
		"xtrabackup_info",
		"xtrabackup_checkpoints",
		"backup-my.cnf",
	}

	for _, file := range requiredFiles {
		filePath := filepath.Join(backupPath, file)
		if _, err := os.Stat(filePath); os.IsNotExist(err) {
			return fmt.Errorf("required file missing: %s", file)
		}
	}

	// 解析并验证 xtrabackup_info
	infoFile := filepath.Join(backupPath, "xtrabackup_info")
	xtraInfo, err := ParseXtraInfo(infoFile)
	if err != nil {
		return fmt.Errorf("failed to validate xtrabackup_info: %w", err)
	}

	// 验证 LSN 信息
	if xtraInfo.LSNFrom == "" || xtraInfo.LSNTo == "" {
		return fmt.Errorf("invalid LSN information in backup")
	}

	logger.Info("Backup validation completed successfully")
	return nil
}

// GetBackupInfo 获取备份信息
func (m *XtraBackupManager) GetBackupInfo(backupPath string) (*XtraInfo, error) {
	infoFile := filepath.Join(backupPath, "xtrabackup_info")
	return ParseXtraInfo(infoFile)
}
