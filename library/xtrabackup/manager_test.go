package xtrabackup

import (
	"context"
	"fmt"
	"testing"
)

// TestBasicFunctionality 基本功能测试
func TestBasicFunctionality(t *testing.T) {
	// 创建测试配置
	config := &Config{
		BinPath:         "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/innobackupex",
		XbstreamPath:    "bin/percona-xtrabackup-2.4.15-Linux-x86_64/bin/xbstream",
		MySQLBaseDir:    "/home/<USER>/mysql",
		MySQLUser:       "backup_user",
		MySQLPassword:   "backup_password",
		MySQLSocket:     "/home/<USER>/mysql/tmp/mysql.sock",
		ThrottleRate:    100,
		CompressLevel:   6,
		CompressThreads: 4,
		UseMemory:       "5G",
		StreamFormat:    "xbstream",
		NoTimestamp:     true,
		SlaveInfo:       true,
	}

	// 创建管理器
	manager := NewManager(config)

	// 测试基本状态
	fmt.Printf("Initial status: %v\n", manager.GetStatus())
	fmt.Printf("Is running: %v\n", manager.IsRunning())

	// 测试限速控制
	fmt.Println("Testing throttle rate control...")
	if err := manager.SetThrottleRate(200); err != nil {
		fmt.Printf("Failed to set throttle rate: %v\n", err)
	} else {
		fmt.Println("Throttle rate set to 200 MB/s successfully")
	}

	// 测试进度监控
	fmt.Println("Testing progress monitoring...")
	manager.monitor.SetCallback(func(info *ProgressInfo) {
		fmt.Printf("Progress callback: Status=%v, Progress=%.2f%%\n", 
			info.Status, info.Progress)
	})

	// 模拟状态变化
	manager.monitor.SetStatus(StatusRunning)
	manager.monitor.SetProgress(50.0)
	manager.monitor.SetStatus(StatusCompleted)

	// 测试进程检查（这个会失败，因为没有真实的备份进程）
	fmt.Println("Testing process check...")
	running, err := manager.CheckProcess()
	if err != nil {
		fmt.Printf("Process check failed (expected): %v\n", err)
	} else {
		fmt.Printf("Process running: %v\n", running)
	}

	// 测试配置验证
	fmt.Println("Testing configuration...")
	if manager.config.ThrottleRate != 200 {
		fmt.Printf("Throttle rate not updated correctly: %d\n", manager.config.ThrottleRate)
	} else {
		fmt.Println("Configuration updated correctly")
	}

	fmt.Println("Basic functionality test completed")
}

// TestBackupOptionsValidation 备份选项验证测试
func TestBackupOptionsValidation(t *testing.T) {
	config := &Config{
		BinPath:      "bin/innobackupex",
		MySQLBaseDir: "/home/<USER>/mysql",
		MySQLUser:    "backup_user",
		MySQLPassword: "backup_password",
	}

	manager := NewManager(config)

	// 测试全量备份选项
	fullOpts := &BackupOptions{
		TargetDir:   "/backup/test/full",
		ExtraLSNDir: "/tmp/test_lsn",
	}

	fmt.Println("Testing full backup command generation...")
	cmd, err := manager.buildBackupCommand(fullOpts)
	if err != nil {
		fmt.Printf("Failed to build full backup command: %v\n", err)
	} else {
		fmt.Printf("Full backup command: %s\n", cmd)
	}

	// 测试增量备份选项
	incOpts := &BackupOptions{
		Type:        BackupTypeIncremental,
		LSNPosition: "123456789",
		TargetDir:   "/backup/test/inc",
		ExtraLSNDir: "/tmp/test_lsn_inc",
	}

	fmt.Println("Testing incremental backup command generation...")
	cmd, err = manager.buildBackupCommand(incOpts)
	if err != nil {
		fmt.Printf("Failed to build incremental backup command: %v\n", err)
	} else {
		fmt.Printf("Incremental backup command: %s\n", cmd)
	}

	// 测试远程备份选项
	remoteOpts := &BackupOptions{
		TargetDir:   "/backup/test/remote",
		RemoteHost:  "backup-server",
		RemotePath:  "/remote/backup/test",
		SSHKeyPath:  "/path/to/ssh/key",
	}

	fmt.Println("Testing remote backup command generation...")
	cmd, err = manager.buildBackupCommand(remoteOpts)
	if err != nil {
		fmt.Printf("Failed to build remote backup command: %v\n", err)
	} else {
		fmt.Printf("Remote backup command: %s\n", cmd)
	}

	fmt.Println("Backup options validation test completed")
}

// TestUtilityFunctions 工具函数测试
func TestUtilityFunctions(t *testing.T) {
	fmt.Println("Testing utility functions...")

	// 测试限速控制器
	limiter := NewRateLimiter(100)
	fmt.Printf("Initial rate: %d MB/s, Enabled: %v\n", limiter.GetRate(), limiter.IsEnabled())

	limiter.SetRate(200)
	fmt.Printf("Updated rate: %d MB/s, Enabled: %v\n", limiter.GetRate(), limiter.IsEnabled())

	limiter.SetRate(0)
	fmt.Printf("Disabled rate: %d MB/s, Enabled: %v\n", limiter.GetRate(), limiter.IsEnabled())

	// 测试进度监控器
	monitor := NewProgressMonitor()
	fmt.Printf("Initial progress: %+v\n", monitor.GetProgress())

	monitor.SetStatus(StatusRunning)
	monitor.SetProgress(75.5)
	fmt.Printf("Updated progress: %+v\n", monitor.GetProgress())

	// 测试进程管理器
	procMgr := NewProcessManager()
	procMgr.SetPID(12345)
	procMgr.SetCommand("test command")
	fmt.Printf("Process ID: %d\n", procMgr.GetPID())
	fmt.Printf("Command: %s\n", procMgr.GetCommand())
	fmt.Printf("Start time: %v\n", procMgr.GetStartTime())

	// 测试并行复制管理器
	config := &Config{MySQLBaseDir: "/home/<USER>/mysql"}
	replMgr := NewReplicationManager(config)
	fmt.Printf("Replication manager created with config: %+v\n", replMgr.config)

	fmt.Println("Utility functions test completed")
}

// TestErrorHandling 错误处理测试
func TestErrorHandling(t *testing.T) {
	fmt.Println("Testing error handling...")

	config := &Config{
		BinPath:      "bin/innobackupex",
		MySQLBaseDir: "/home/<USER>/mysql",
	}

	manager := NewManager(config)

	// 测试无效的增量备份（没有 LSN）
	ctx := context.Background()
	invalidIncOpts := &BackupOptions{
		TargetDir: "/backup/test/invalid",
	}

	fmt.Println("Testing invalid incremental backup...")
	_, err := manager.IncrementalBackup(ctx, invalidIncOpts)
	if err != nil {
		fmt.Printf("Expected error for invalid incremental backup: %v\n", err)
	} else {
		fmt.Println("ERROR: Should have failed for invalid incremental backup")
	}

	// 测试重复备份
	manager.status = StatusRunning
	fmt.Println("Testing duplicate backup...")
	_, err = manager.FullBackup(ctx, &BackupOptions{TargetDir: "/backup/test"})
	if err != nil {
		fmt.Printf("Expected error for duplicate backup: %v\n", err)
	} else {
		fmt.Println("ERROR: Should have failed for duplicate backup")
	}

	// 重置状态
	manager.status = StatusIdle

	// 测试无效的控制操作
	fmt.Println("Testing invalid control operations...")
	err = manager.Pause()
	if err != nil {
		fmt.Printf("Expected error for pause when not running: %v\n", err)
	}

	err = manager.Resume()
	if err != nil {
		fmt.Printf("Expected error for resume when not paused: %v\n", err)
	}

	fmt.Println("Error handling test completed")
}


