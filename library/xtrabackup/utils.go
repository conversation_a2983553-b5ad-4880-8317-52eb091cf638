package xtrabackup

import (
	"fmt"
	"os"
	"regexp"
	"strings"
	"sync"
	"time"

	"mdc-agent/library/logger"
	"mdc-agent/library/utils"
)

// NewRateLimiter 创建限速控制器
func NewRateLimiter(rate int64) *RateLimiter {
	return &RateLimiter{
		rate:    rate,
		enabled: rate > 0,
	}
}

// SetRate 设置限速
func (r *RateLimiter) SetRate(rate int64) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.rate = rate
	r.enabled = rate > 0
}

// GetRate 获取当前限速
func (r *RateLimiter) GetRate() int64 {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.rate
}

// IsEnabled 检查是否启用限速
func (r *RateLimiter) IsEnabled() bool {
	r.mutex.RLock()
	defer r.mutex.RUnlock()
	return r.enabled
}

// NewProgressMonitor 创建进度监控器
func NewProgressMonitor() *ProgressMonitor {
	return &ProgressMonitor{
		info: &ProgressInfo{
			Status:   StatusIdle,
			Progress: 0,
		},
	}
}

// SetStatus 设置状态
func (p *ProgressMonitor) SetStatus(status BackupStatus) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.info.Status = status
	p.info.Message = p.getStatusMessage(status)

	if p.callback != nil {
		p.callback(p.info)
	}
}

// SetProgress 设置进度
func (p *ProgressMonitor) SetProgress(progress float64) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.info.Progress = progress

	if p.callback != nil {
		p.callback(p.info)
	}
}

// GetProgress 获取进度信息
func (p *ProgressMonitor) GetProgress() *ProgressInfo {
	p.mutex.RLock()
	defer p.mutex.RUnlock()

	// 返回副本避免并发问题
	return &ProgressInfo{
		Status:      p.info.Status,
		Progress:    p.info.Progress,
		CurrentFile: p.info.CurrentFile,
		ProcessedMB: p.info.ProcessedMB,
		TotalMB:     p.info.TotalMB,
		Speed:       p.info.Speed,
		ETA:         p.info.ETA,
		Message:     p.info.Message,
	}
}

// SetCallback 设置进度回调函数
func (p *ProgressMonitor) SetCallback(callback func(*ProgressInfo)) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.callback = callback
}

// getStatusMessage 获取状态消息
func (p *ProgressMonitor) getStatusMessage(status BackupStatus) string {
	switch status {
	case StatusIdle:
		return "Ready to start backup"
	case StatusRunning:
		return "Backup in progress"
	case StatusPaused:
		return "Backup paused"
	case StatusCompleted:
		return "Backup completed successfully"
	case StatusFailed:
		return "Backup failed"
	case StatusStopped:
		return "Backup stopped"
	default:
		return "Unknown status"
	}
}

// NewProcessManager 创建进程管理器
func NewProcessManager() *ProcessManager {
	return &ProcessManager{}
}

// SetPID 设置进程 ID
func (p *ProcessManager) SetPID(pid int) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.pid = pid
}

// GetPID 获取进程 ID
func (p *ProcessManager) GetPID() int {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.pid
}

// SetCommand 设置执行命令
func (p *ProcessManager) SetCommand(cmd string) {
	p.mutex.Lock()
	defer p.mutex.Unlock()
	p.cmd = cmd
	p.startTime = time.Now()
}

// GetCommand 获取执行命令
func (p *ProcessManager) GetCommand() string {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.cmd
}

// GetStartTime 获取启动时间
func (p *ProcessManager) GetStartTime() time.Time {
	p.mutex.RLock()
	defer p.mutex.RUnlock()
	return p.startTime
}

// ParseXtraInfo 解析 xtrabackup_info 文件
func ParseXtraInfo(infoFile string) (*XtraInfo, error) {
	content, err := os.ReadFile(infoFile)
	if err != nil {
		return nil, fmt.Errorf("failed to read xtrabackup_info file: %w", err)
	}

	info := &XtraInfo{}
	contentStr := string(content)

	// 解析数据大小
	if match := regexp.MustCompile(`compressed_size\s=\s(\S+)`).FindStringSubmatch(contentStr); len(match) == 2 {
		info.DataSize = match[1]
	}

	// 解析起始 LSN
	if match := regexp.MustCompile(`innodb_from_lsn\s=\s([0-9]+)`).FindStringSubmatch(contentStr); len(match) == 2 {
		info.LSNFrom = match[1]
	}

	// 解析结束 LSN
	if match := regexp.MustCompile(`innodb_to_lsn\s=\s([0-9]+)`).FindStringSubmatch(contentStr); len(match) == 2 {
		info.LSNTo = match[1]
	}

	// 解析结束时间
	if match := regexp.MustCompile(`end_time\s=\s(.*)`).FindStringSubmatch(contentStr); len(match) == 2 {
		info.EndTime = match[1]
	}

	// 验证必要字段
	if info.LSNFrom == "" || info.LSNTo == "" {
		return nil, fmt.Errorf("failed to parse required LSN information from xtrabackup_info")
	}

	return info, nil
}

// NewReplicationManager 创建并行复制管理器
func NewReplicationManager(config *Config) *ReplicationManager {
	return &ReplicationManager{
		config: &ReplicationConfig{
			Workers:             16,
			PreserveCommitOrder: true,
			AutoManage:          true,
		},
		mysqlBaseDir: config.MySQLBaseDir,
	}
}

// ReplicationManager 并行复制管理器
type ReplicationManager struct {
	config           *ReplicationConfig
	mysqlBaseDir     string
	originalSettings map[string]string
	mutex            sync.RWMutex
}

// ManageParallelReplication 管理并行复制
func (r *ReplicationManager) ManageParallelReplication(disable bool) error {
	if !r.config.AutoManage {
		return nil
	}

	if disable {
		return r.disableParallelReplication()
	} else {
		return r.enableParallelReplication()
	}
}

// disableParallelReplication 禁用并行复制
func (r *ReplicationManager) disableParallelReplication() error {
	// 检查当前并行复制设置
	checkCmd := fmt.Sprintf("%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -e \"show variables like 'slave_parallel_workers';\" | grep 'slave_parallel_workers' | awk '{print $2}'",
		r.mysqlBaseDir, r.mysqlBaseDir)

	workers, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", checkCmd},
		Timeout: 5 * time.Second,
		Retry:   3,
	})
	if err != nil {
		return fmt.Errorf("failed to check parallel replication status: %w", err)
	}

	workers = strings.TrimSpace(workers)
	if workers == "0" {
		logger.Info("Parallel replication is already disabled")
		return nil
	}

	// 保存原始设置
	r.mutex.Lock()
	if r.originalSettings == nil {
		r.originalSettings = make(map[string]string)
	}
	r.originalSettings["slave_parallel_workers"] = workers
	r.mutex.Unlock()

	// 禁用并行复制
	disableCmd := fmt.Sprintf("%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -e \"stop slave; set global slave_parallel_workers=0; set global slave_preserve_commit_order=0; start slave;\"",
		r.mysqlBaseDir, r.mysqlBaseDir)

	if _, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", disableCmd},
		Timeout: 5 * time.Second,
		Retry:   3,
	}); err != nil {
		return fmt.Errorf("failed to disable parallel replication: %w", err)
	}

	logger.Info("Parallel replication disabled for backup")
	return nil
}

// enableParallelReplication 启用并行复制
func (r *ReplicationManager) enableParallelReplication() error {
	r.mutex.RLock()
	originalWorkers := r.originalSettings["slave_parallel_workers"]
	r.mutex.RUnlock()

	if originalWorkers == "" || originalWorkers == "0" {
		logger.Info("Parallel replication was not enabled originally")
		return nil
	}

	// 恢复并行复制设置
	enableCmd := fmt.Sprintf("%s/bin/mysql --defaults-file=%s/etc/user.root.cnf -e \"stop slave; set global slave_parallel_workers=%s; set global slave_preserve_commit_order=1; start slave;\"",
		r.mysqlBaseDir, r.mysqlBaseDir, originalWorkers)

	if _, err := utils.ExecCommand(&utils.Shell{
		Command: "bash",
		Args:    []string{"-c", enableCmd},
		Timeout: 5 * time.Second,
		Retry:   3,
	}); err != nil {
		return fmt.Errorf("failed to enable parallel replication: %w", err)
	}

	logger.Info("Parallel replication restored to %s workers", originalWorkers)
	return nil
}
