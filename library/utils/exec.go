package utils

import (
	"context"
	"fmt"
	"os"
	"os/exec"
	"strconv"
	"strings"
	"time"
)

type Shell struct {
	Command string
	Args    []string
	Timeout time.Duration
	Retry   int
}

// 执行命令行程序
func execCommand(cfg *Shell) (string, error) {
	if cfg.Timeout == 0 {
		cfg.Timeout = 3 * time.Second
	}
	if cfg.Retry == 0 {
		cfg.Retry = 1
	}
	retry := cfg.Retry

	var output []byte
	var err error
	for i := 0; i < cfg.Retry; i++ {
		ctx, cancel := context.WithTimeout(context.Background(), cfg.Timeout)
		output, err = exec.CommandContext(ctx, cfg.Command, cfg.Args...).CombinedOutput()
		cancel()
		if err != nil && i < retry-1 {
			continue
		}
		break
	}

	return string(output), err
}

var ExecCommand = execCommand

func SyncExecShell(cmdStr string) (outStr string, status int, err error) {
	defer func() {
		r := recover()
		if errs, ok := r.(error); ok {
			const size = 4096
			buf := make([]byte, size)
			buf = buf[:runtime.Stack(buf, false)]
			err = errors.New(fmt.Sprintf("err=%v, stack=%v", errs, string(buf)))
		}
	}()
	command := exec.Command("/bin/bash", "-c", cmdStr)
	out, err := command.Output()
	statusCode, ok := command.ProcessState.Sys().(syscall.WaitStatus)
	if ok {
		status = statusCode.ExitStatus()
	} else if err != nil {
		// 当无法获取到命令行返回值时，如果err不为空将返回值置为-1
		status = -1
	}
	
	if err != nil {
		//超时退出码为124
		if 124 == status {
			return string(out), status, errors.New(fmt.Sprintf("ExecShell timeout,err=%s", err.Error()))
		}
		if ee, ok := err.(*exec.ExitError); ok {
			return string(out), status, errors.New(fmt.Sprintf("%s,%s", err.Error(), string(ee.Stderr)))
		}
		return string(out), status, err
	}
	return string(out), status, nil
}

// func SyncExecShellNoResult(cmdStr string) (err error) {
// 	defer func() {
// 		r := recover()
// 		if errs, ok := r.(error); ok {
// 			const size = 4096
// 			buf := make([]byte, size)
// 			buf = buf[:runtime.Stack(buf, false)]
// 			err = errors.New(fmt.Sprintf("err=%v, stack=%v", errs, string(buf)))
// 		}
// 	}()
// 	command := exec.Command("/bin/bash", "-c", cmdStr)
// 	err = command.Start()
// 	Log.Notice("Execute shell command. cmdStr=[%v] stderr[%v]",
// 		cmdStr, err)
// 	if err != nil {
// 		return err
// 	}
// 	return nil
// }

// ==========================================
//                  Mock
// ==========================================

var mockedExitStatus chan int
var mockedStdout chan string

func ActivateExecMock(cmdNum ...int) {
	num := 10
	if len(cmdNum) > 0 {
		num = cmdNum[0]
	}
	mockedExitStatus = make(chan int, num)
	mockedStdout = make(chan string, num)
	ExecCommand = fakeExecCommand
}

func DeactivateExecMock() {
	close(mockedExitStatus)
	close(mockedStdout)
	ExecCommand = execCommand
}

func ExpectExec(status int, stdout string) {
	mockedExitStatus <- status
	mockedStdout <- stdout
}

func fakeExecCommand(cfg *Shell) (string, error) {
	cs := []string{"-test.run=TestExecCommandHelper", "--", cfg.Command}
	cs = append(cs, cfg.Args...)
	cmd := exec.Command(os.Args[0], cs...)
	select {
	case status := <-mockedExitStatus:
		es := strconv.Itoa(status)
		cmd.Env = []string{"GO_WANT_HELPER_PROCESS=1", "STDOUT=" + (<-mockedStdout), "EXIT_STATUS=" + es}
	default:
		return "", fmt.Errorf("unexpected exec command %s", cfg.Command)
	}

	stdout, err := cmd.CombinedOutput()
	output := strings.ReplaceAll(string(stdout), "warning: GOCOVERDIR not set, no coverage data emitted\n", "")

	return output, err
}
